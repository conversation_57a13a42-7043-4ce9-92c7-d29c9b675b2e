<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Applicant;
use App\Models\RecommendationLetter;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

/**
 * 推薦函表單測試
 * 
 * 測試推薦函創建時的欄位對應和邏輯正確性
 */
class RecommendationFormTest extends TestCase
{
    use RefreshDatabase;

    protected User $applicantUser;
    protected Applicant $applicant;

    protected function setUp(): void
    {
        parent::setUp();

        // 創建測試申請人
        $this->applicantUser = User::factory()->create([
            'role' => 'applicant',
            'email' => '<EMAIL>'
        ]);

        $this->applicant = Applicant::factory()->create([
            'user_id' => $this->applicantUser->id,
            'external_uid' => 'test001'
        ]);

        // 設定使用者已同意使用者協議
        $this->applicantUser->update(['agreed_to_terms_at' => now()]);
    }

    /**
     * 測試推薦函創建時的欄位對應
     */
    public function test_recommendation_creation_field_mapping()
    {
        // 模擬申請人登入
        $this->actingAs($this->applicantUser);
        Session::put('applicant_id', $this->applicant->id);

        // 準備表單資料
        $formData = [
            'recommender_email' => '<EMAIL>',
            'recommender_name' => '張教授',
            'recommender_title' => '教授',
            'recommender_department' => '國立台灣大學資訊工程學系', // 推薦人所屬單位
            'recommender_phone' => '02-12345678',
            'department_name' => '資訊工程學系', // 申請人報名的系所
            'program_type' => '電腦科學碩士班', // 申請人報名的學程
            'application_form_id' => 'CS_MASTER_2024_001',
        ];

        // 發送請求
        $response = $this->post('/recommendations', $formData);

        // 驗證回應
        $response->assertRedirect('/dashboard');

        // 驗證資料庫記錄
        $this->assertDatabaseHas('recommendation_letters', [
            'applicant_id' => $this->applicant->id,
            'recommender_email' => '<EMAIL>',
            'recommender_name' => '張教授',
            'recommender_title' => '教授',
            'recommender_department' => '國立台灣大學資訊工程學系', // 推薦人所屬
            'department_name' => '資訊工程學系', // 推薦函分類：系所
            'program_type' => '電腦科學碩士班', // 推薦函分類：學程
            'status' => 'pending'
        ]);

        // 驗證推薦人記錄
        $this->assertDatabaseHas('recommenders', [
            'email' => '<EMAIL>',
            'name' => '張教授',
            'title' => '教授',
            'department' => '國立台灣大學資訊工程學系' // 推薦人所屬單位
        ]);
    }

    /**
     * 測試推薦函分類邏輯
     */
    public function test_recommendation_classification_logic()
    {
        $this->actingAs($this->applicantUser);
        Session::put('applicant_id', $this->applicant->id);

        // 創建同一推薦人的不同系所推薦函
        $baseData = [
            'recommender_email' => '<EMAIL>',
            'recommender_name' => '張教授',
            'recommender_title' => '教授',
            'recommender_department' => '國立台灣大學資訊工程學系',
            'recommender_phone' => '02-12345678',
        ];

        // 第一個推薦函：資訊工程學系碩士班
        $this->post('/recommendations', array_merge($baseData, [
            'department_name' => '資訊工程學系',
            'program_type' => '電腦科學碩士班',
            'application_form_id' => 'CS_MASTER_2024_001'
        ]));

        // 第二個推薦函：數學系碩士班
        $this->post('/recommendations', array_merge($baseData, [
            'department_name' => '數學系',
            'program_type' => '應用數學碩士班',
            'application_form_id' => 'MATH_MASTER_2024_001'
        ]));

        // 驗證兩個推薦函都被正確創建且分類不同
        $this->assertDatabaseHas('recommendation_letters', [
            'applicant_id' => $this->applicant->id,
            'recommender_email' => '<EMAIL>',
            'department_name' => '資訊工程學系',
            'program_type' => '電腦科學碩士班'
        ]);

        $this->assertDatabaseHas('recommendation_letters', [
            'applicant_id' => $this->applicant->id,
            'recommender_email' => '<EMAIL>',
            'department_name' => '數學系',
            'program_type' => '應用數學碩士班'
        ]);

        // 驗證只創建了一個推薦人記錄
        $this->assertEquals(1, \App\Models\Recommender::where('email', '<EMAIL>')->count());
    }

    /**
     * 測試重複推薦函檢查
     */
    public function test_duplicate_recommendation_prevention()
    {
        $this->actingAs($this->applicantUser);
        Session::put('applicant_id', $this->applicant->id);

        $formData = [
            'recommender_email' => '<EMAIL>',
            'recommender_name' => '張教授',
            'recommender_title' => '教授',
            'recommender_department' => '國立台灣大學資訊工程學系',
            'department_name' => '資訊工程學系',
            'program_type' => '電腦科學碩士班',
            'application_form_id' => 'CS_MASTER_2024_001',
        ];

        // 第一次創建
        $response1 = $this->post('/recommendations', $formData);
        $response1->assertRedirect('/dashboard');

        // 第二次創建相同的推薦函
        $response2 = $this->post('/recommendations', $formData);
        $response2->assertRedirect('/dashboard');
        $response2->assertSessionHasErrors();

        // 驗證只有一個推薦函記錄
        $this->assertEquals(1, RecommendationLetter::where([
            'applicant_id' => $this->applicant->id,
            'recommender_email' => '<EMAIL>',
            'department_name' => '資訊工程學系',
            'program_type' => '電腦科學碩士班'
        ])->count());
    }

    /**
     * 測試必填欄位驗證
     */
    public function test_required_field_validation()
    {
        $this->actingAs($this->applicantUser);
        Session::put('applicant_id', $this->applicant->id);

        // 測試缺少必填欄位
        $response = $this->post('/recommendations', [
            'recommender_name' => '張教授',
            // 缺少 recommender_email
            'department_name' => '資訊工程學系',
            'program_type' => '電腦科學碩士班',
            'application_form_id' => 'CS_MASTER_2024_001'
        ]);

        $response->assertSessionHasErrors(['recommender_email']);
    }

    /**
     * 測試選填欄位處理
     */
    public function test_optional_field_handling()
    {
        $this->actingAs($this->applicantUser);
        Session::put('applicant_id', $this->applicant->id);

        // 只提供必填欄位
        $formData = [
            'recommender_email' => '<EMAIL>',
            'recommender_name' => '張教授',
            'department_name' => '資訊工程學系',
            'program_type' => '電腦科學碩士班',
            'application_form_id' => 'CS_MASTER_2024_001'
            // 不提供選填欄位
        ];

        $response = $this->post('/recommendations', $formData);
        $response->assertRedirect('/dashboard');

        // 驗證選填欄位為空值
        $this->assertDatabaseHas('recommendation_letters', [
            'applicant_id' => $this->applicant->id,
            'recommender_email' => '<EMAIL>',
            'recommender_title' => null,
            'recommender_department' => '',
            'recommender_phone' => null,
        ]);
    }
}
