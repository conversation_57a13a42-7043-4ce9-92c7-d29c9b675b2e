<?php

namespace Database\Factories;

use App\Models\Applicant;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RecommendationLetter>
 */
class RecommendationLetterFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departments = [
            'Computer Science & Engineering',
            'Electrical Engineering',
            'Mathematics Department',
            'Physics Department',
            'Chemistry Department',
            'Biology Department',
            'Economics Department',
            'Business Administration'
        ];

        $recommenderDepartments = [
            'Theory & Systems',
            'AI & Machine Learning',
            'Software Engineering',
            'Computer Architecture',
            'Pure Math',
            'Applied Math',
            'Quantum Physics',
            'Organic Chemistry'
        ];

        $statuses = ['pending', 'no_response', 'submitted', 'withdrawn', 'declined'];
        $programTypes = ['master', 'phd'];

        $status = fake()->randomElement($statuses);

        return [
            'applicant_id' => Applicant::factory(),
            'application_form_id' => 'APP-' . fake()->year() . '-' . fake()->randomNumber(6, true),
            'recommender_email' => fake()->safeEmail(),
            'recommender_name' => fake()->name(),
            'recommender_title' => fake()->randomElement(['Professor', 'Associate Professor', 'Assistant Professor', 'Lecturer']),
            'recommender_department' => fake()->randomElement($recommenderDepartments),
            'recommender_phone' => fake()->optional()->phoneNumber(),
            'department_name' => fake()->randomElement($departments),
            'program_type' => fake()->randomElement($programTypes),
            'status' => $status,
            'submitted_at' => $status === 'submitted' ? fake()->dateTimeBetween('-1 month', 'now') : null,
            'pdf_path' => $status === 'submitted' ? 'recommendations/' . Str::random(40) . '.pdf' : null,
            'questionnaire_data' => $status === 'submitted' ? $this->generateQuestionnaireData() : null,
        ];
    }

    /**
     * Generate sample questionnaire data.
     */
    private function generateQuestionnaireData(): array
    {
        return [
            'academic_performance' => fake()->randomElement(['Excellent', 'Very Good', 'Good', 'Average']),
            'research_potential' => fake()->randomElement(['Outstanding', 'Very High', 'High', 'Moderate']),
            'communication_skills' => fake()->randomElement(['Excellent', 'Very Good', 'Good', 'Fair']),
            'leadership_qualities' => fake()->randomElement(['Strong', 'Moderate', 'Developing', 'Limited']),
            'overall_recommendation' => fake()->randomElement(['Highly Recommend', 'Recommend', 'Recommend with Reservations']),
            'additional_comments' => fake()->optional()->paragraph(),
        ];
    }

    /**
     * Create a pending recommendation letter.
     */
    public function pending(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'pending',
            'submitted_at' => null,
            'pdf_path' => null,
            'questionnaire_data' => null,
        ]);
    }

    /**
     * Create a submitted recommendation letter.
     */
    public function submitted(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'submitted',
            'submitted_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'pdf_path' => 'recommendations/' . Str::random(40) . '.pdf',
            'questionnaire_data' => $this->generateQuestionnaireData(),
        ]);
    }

    /**
     * Create a recommendation letter for a specific department.
     */
    public function forDepartment(string $department): static
    {
        return $this->state(fn(array $attributes) => [
            'department_name' => $department,
        ]);
    }

    /**
     * Create a recommendation letter for a specific program type.
     */
    public function forProgram(string $programType): static
    {
        return $this->state(fn(array $attributes) => [
            'program_type' => $programType,
        ]);
    }

    /**
     * Create a recommendation letter for a specific applicant.
     */
    public function forApplicant(Applicant $applicant): static
    {
        return $this->state(fn(array $attributes) => [
            'applicant_id' => $applicant->id,
        ]);
    }
}
