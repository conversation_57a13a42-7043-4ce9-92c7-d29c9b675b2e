<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\Recommender;
use App\Models\RecommendationLetter;
use App\Services\EmailService;
use App\Services\FileStorageService;
use App\Services\PdfService;
use App\Services\RecommenderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RecommendationController extends Controller
{
  /**
   * 建立推薦函
   */
  public function createRecommendation(Request $request)
  {
    try {
      $request->validate([
        'recommender_email' => 'required|email',
        'recommender_name' => 'required|string|max:255',
        'recommender_title' => 'nullable|string|max:255',
        'recommender_department' => 'nullable|string|max:255',
        'recommender_phone' => 'nullable|string|max:20',
        'department_name' => 'required|string|max:255',
        'program_type' => 'required|string|max:255',
        'application_form_id' => 'required|string|max:255',
      ]);

      $user = Auth::user();
      $applicantId = Session::get('applicant_id');

      if (!$applicantId) {
        $applicant = Applicant::where('user_id', $user->id)->first();
        $applicantId = $applicant?->id;
      }

      if (!$applicantId) {
        return back()->withErrors([
          'applicant' => '無法確認您的申請人身份，請重新登入'
        ]);
      }

      // Check for duplicate recommendation
      $existingRecommendation = RecommendationLetter::where('applicant_id', $applicantId)
        ->where('recommender_email', $request->recommender_email)
        ->where('department_name', $request->department_name)
        ->where('program_type', $request->program_type)
        ->whereNotIn('status', ['withdrawn'])
        ->first();

      if ($existingRecommendation) {
        return back()->withErrors([
          'duplicate' => '您已經向此推薦人發送過相同科系的推薦函邀請'
        ]);
      }

      // Log the incoming request data for debugging
      Log::info('Creating recommendation with data:', [
        'recommender_department' => $request->recommender_department,
        'department_name' => $request->department_name,
        'all_request_data' => $request->all()
      ]);

      // 取得申請人的考試資訊
      $applicant = Applicant::find($applicantId);
      $examInfo = null;
      if ($applicant) {
        $examInfo = [
          'exam_year' => $applicant->exam_year,
          'exam_id' => $applicant->exam_id,
        ];
      }

      // Create or get recommender user and profile
      $recommenderService = new RecommenderService();
      $recommenderResult = $recommenderService->createOrGetRecommender([
        'email' => $request->recommender_email,
        'name' => $request->recommender_name,
        'title' => $request->recommender_title,
        'department' => $request->recommender_department ?? '',
        'phone' => $request->recommender_phone,
      ], $examInfo);

      $recommender = $recommenderResult['recommender'];

      // Create recommendation letter
      $recommendation = RecommendationLetter::create([
        'applicant_id' => $applicantId,
        'recommender_id' => $recommender->id, // 關聯推薦人 ID
        'application_form_id' => $request->application_form_id,
        'recommender_email' => $request->recommender_email,
        'recommender_name' => $request->recommender_name,
        'recommender_title' => $request->recommender_title,
        'recommender_department' => $request->recommender_department ?? '',
        'recommender_phone' => $request->recommender_phone,
        'department_name' => $request->department_name,
        'program_type' => $request->program_type,
        'status' => RecommendationLetter::STATUS_PENDING,
      ]);

      // Send invitation email
      $emailService = new EmailService();
      $emailSent = $emailService->sendInvitationEmail($recommendation);

      // Prepare response message
      $message = '推薦函邀請已創建';
      if ($recommenderResult['created']) {
        $message .= '，推薦人帳號已自動建立';
      }
      if ($emailSent) {
        $message .= '並發送邀請信';
      } else {
        $message .= '，但邀請信發送失敗';
      }

      return redirect()->route('dashboard')->with('success', $message);
    } catch (\Illuminate\Validation\ValidationException $e) {
      return back()->withErrors($e->errors())->withInput();
    } catch (\Exception $e) {
      Log::error('Error creating recommendation: ' . $e->getMessage(), [
        'user_id' => Auth::id(),
        'request_data' => $request->all(),
        'trace' => $e->getTraceAsString()
      ]);

      return back()->withErrors(['system' => '創建推薦函時發生錯誤，請稍後再試'])->withInput();
    }
  }

  /**
   * 發送提醒
   */
  public function sendReminder($id)
  {
    $user = Auth::user();
    $applicantId = Session::get('applicant_id');

    if (!$applicantId) {
      $applicant = Applicant::where('user_id', $user->id)->first();
      $applicantId = $applicant?->id;
    }

    $recommendation = RecommendationLetter::where('id', $id)
      ->where('applicant_id', $applicantId)
      ->where('status', RecommendationLetter::STATUS_PENDING)
      ->first();

    if (!$recommendation) {
      return back()->withErrors(['recommendation' => '推薦函不存在或狀態不允許發送提醒']);
    }

    // 檢查是否可以發送提醒（使用模型方法）
    if (!$recommendation->canSendReminder()) {
      $cooldownHours = 24;
      $nextAllowedTime = $recommendation->last_reminded_at->addHours($cooldownHours);
      $remainingHours = now()->diffInHours($nextAllowedTime, false);
      $remainingMinutes = now()->diffInMinutes($nextAllowedTime, false) % 60;

      return back()->withErrors([
        'cooldown' => '提醒信發送過於頻繁，請等待 ' . abs($remainingHours) . ' 小時 ' . abs($remainingMinutes) . ' 分鐘後再試'
      ]);
    }

    $emailService = new EmailService();
    $success = $emailService->sendReminderEmail($recommendation);

    if ($success) {
      return back()->with([
        'success' => '提醒信已發送給推薦人',
        'last_reminded_at' => $recommendation->last_reminded_at->toISOString(),
      ]);
    } else {
      return back()->withErrors(['email' => '發送提醒信失敗，請稍後再試']);
    }
  }

  /**
   * 撤回推薦函
   */
  public function withdraw($id)
  {
    $user = Auth::user();
    $applicantId = Session::get('applicant_id');

    if (!$applicantId) {
      $applicant = Applicant::where('user_id', $user->id)->first();
      $applicantId = $applicant?->id;
    }

    $recommendation = RecommendationLetter::where('id', $id)
      ->where('applicant_id', $applicantId)
      ->whereIn('status', ['pending', 'no_response'])
      ->first();

    if (!$recommendation) {
      return back()->withErrors(['recommendation' => '推薦函不存在或狀態不允許撤回']);
    }

    $recommendation->update([
      'status' => 'withdrawn',
    ]);

    return back()->with([
      'success' => '推薦函已撤回',
      'recommendation' => $recommendation,
    ]);
  }


  /**
   * 婉拒推薦函
   */
  public function declineRecommendation($id)
  {
    try {
      $user = Auth::user();

      if (!$user || $user->role !== 'recommender') {
        return back()->withErrors([
          'permission' => '您沒有權限執行此操作'
        ]);
      }

      $recommendation = RecommendationLetter::where('id', $id)
        ->where('recommender_email', $user->email)
        ->where('status', RecommendationLetter::STATUS_PENDING)
        ->first();

      if (!$recommendation) {
        return back()->withErrors([
          'recommendation' => '找不到可操作的推薦函，可能已被處理或不存在'
        ]);
      }

      // 使用模型方法拒絕推薦函
      if (!$recommendation->decline()) {
        return back()->withErrors([
          'recommendation' => '推薦函狀態不允許此操作'
        ]);
      }

      return redirect()->route('dashboard')->with('success', '已拒絕推薦邀請');
    } catch (\Exception $e) {
      Log::error('Error declining recommendation: ' . $e->getMessage(), [
        'user_id' => Auth::id(),
        'recommendation_id' => $id
      ]);

      return back()->withErrors([
        'system' => '系統暫時無法處理您的請求，請稍後再試'
      ]);
    }
  }

  /**
   * 提交推薦函（包含 PDF 或問卷資料）
   */
  public function submitRecommendation(Request $request, $id)
  {
    try {
      $request->validate([
        'pdf_file' => 'nullable|file|mimes:pdf|max:10240', // 10MB max
        'questionnaire_data' => 'nullable|string', // 改為字串，因為前端傳送 JSON 字串
      ]);

      $user = Auth::user();

      $recommendation = RecommendationLetter::where('id', $id)
        ->where('recommender_email', $user->email)
        ->where('status', RecommendationLetter::STATUS_PENDING)
        ->first();

      if (!$recommendation) {
        return back()->withErrors([
          'recommendation' => '找不到可提交的推薦函'
        ]);
      }

      $pdfService = new PdfService();
      $fileStorageService = new FileStorageService();
      $submissionType = null;
      $submissionData = [];

      // Handle PDF upload
      if ($request->hasFile('pdf_file')) {
        $file = $request->file('pdf_file');

        // 驗證 PDF 檔案
        if (!$pdfService->validatePdf($file->getRealPath())) {
          return back()->withErrors([
            'pdf_file' => '請上傳有效的 PDF 檔案'
          ]);
        }

        // 使用新的檔案存儲服務儲存PDF檔案（年度/考生/PDF分類）
        $pdfPath = $fileStorageService->storePdfFile($file, $recommendation);

        $submissionType = RecommendationLetter::SUBMISSION_TYPE_PDF;
        $submissionData['pdf_path'] = $pdfPath;

        Log::info('推薦函 PDF 上傳成功', [
          'recommendation_id' => $id,
          'file_path' => $pdfPath,
          'file_size' => $file->getSize(),
        ]);
      }

      // Handle questionnaire data and generate PDF
      if ($request->questionnaire_data) {
        // 解析 JSON 字串為陣列
        try {
          $questionnaireData = json_decode($request->questionnaire_data, true);

          if (json_last_error() !== JSON_ERROR_NONE) {
            return back()->withErrors([
              'questionnaire_data' => '問卷資料格式錯誤：' . json_last_error_msg()
            ]);
          }

          if (!is_array($questionnaireData)) {
            return back()->withErrors([
              'questionnaire_data' => '問卷資料必須是有效的 JSON 物件'
            ]);
          }
        } catch (\Exception $e) {
          return back()->withErrors([
            'questionnaire_data' => '無法解析問卷資料：' . $e->getMessage()
          ]);
        }

        try {
          // 從問卷資料生成 PDF
          $pdfContent = $pdfService->generateRecommendationPdf($recommendation, $questionnaireData);

          // 使用新的檔案存儲服務儲存生成的PDF（年度/考生/PDF分類）
          $pdfPath = $fileStorageService->storeGeneratedPdf($pdfContent, $recommendation);

          $submissionType = RecommendationLetter::SUBMISSION_TYPE_QUESTIONNAIRE;
          $submissionData['questionnaire_data'] = $questionnaireData;
          $submissionData['pdf_path'] = $pdfPath;

          Log::info('推薦函問卷 PDF 生成成功', [
            'recommendation_id' => $id,
            'file_path' => $pdfPath,
          ]);
        } catch (\Exception $e) {
          Log::error('推薦函問卷 PDF 生成失敗', [
            'recommendation_id' => $id,
            'error' => $e->getMessage(),
          ]);

          return back()->withErrors([
            'questionnaire' => '無法從問卷資料生成 PDF，請稍後再試'
          ]);
        }
      }

      // 檢查是否有提交內容
      if (!$submissionType) {
        return back()->withErrors([
          'submission' => '請上傳 PDF 檔案或填寫問卷'
        ]);
      }

      // 使用模型方法提交推薦函
      if (!$recommendation->submit($submissionType, $submissionData)) {
        return back()->withErrors([
          'submission' => '推薦函狀態不允許提交'
        ]);
      }

      // 發送提交通知給申請人
      $emailService = new EmailService();
      $emailService->sendSubmissionNotificationEmail($recommendation);

      // 準備回應資料
      $response = [
        'message' => '推薦函已成功提交',
        'recommendation' => $recommendation->fresh(),
        'submission_type' => $submissionType,
      ];

      if (isset($submissionData['pdf_path'])) {
        $response['pdf_info'] = [
          'path' => $submissionData['pdf_path'],
          'url' => asset('storage/' . $submissionData['pdf_path']),
        ];
      }

      Log::info('推薦函提交成功', [
        'recommendation_id' => $id,
        'submission_type' => $submissionType,
        'user_id' => Auth::id(),
      ]);

      return redirect()->route('dashboard')->with('success', '推薦函已成功提交');
    } catch (\Illuminate\Validation\ValidationException $e) {
      return back()->withErrors($e->errors());
    } catch (\Exception $e) {
      Log::error('提交推薦函時發生錯誤', [
        'recommendation_id' => $id,
        'error' => $e->getMessage(),
        'user_id' => Auth::id(),
      ]);

      return back()->withErrors([
        'system' => '系統暫時無法處理您的請求，請稍後再試'
      ]);
    }
  }
}
