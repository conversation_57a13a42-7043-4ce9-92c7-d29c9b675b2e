<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 推薦函系統配置
    |--------------------------------------------------------------------------
    |
    | 此配置文件包含推薦函系統的各種設定，包括截止日期、郵件設定、
    | PDF 生成設定等。
    |
    */

    /*
    |--------------------------------------------------------------------------
    | 系統基本設定
    |--------------------------------------------------------------------------
    */
    'system' => [
        'name' => env('RECOMMENDATION_SYSTEM_NAME', '推薦函管理系統'),
        'version' => '1.0.0',
        'admin_email' => env('RECOMMENDATION_ADMIN_EMAIL', '<EMAIL>'),
        'support_email' => env('RECOMMENDATION_SUPPORT_EMAIL', '<EMAIL>'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 截止日期設定
    |--------------------------------------------------------------------------
    */
    'deadline' => env('RECOMMENDATION_DEADLINE', '2024-03-15 23:59:59'),

    /*
    |--------------------------------------------------------------------------
    | 推薦人登入設定
    |--------------------------------------------------------------------------
    */
    'recommender' => [
        // Token 有效期限（小時）
        'token_expiration_hours' => env('RECOMMENDER_TOKEN_EXPIRATION', 168), // 7 天

        // 是否允許推薦人修改個人資料
        'allow_profile_edit' => env('RECOMMENDER_ALLOW_PROFILE_EDIT', true),

        // 推薦人登入後的預設重導向頁面
        'default_redirect' => '/dashboard',
    ],

    /*
    |--------------------------------------------------------------------------
    | 郵件設定
    |--------------------------------------------------------------------------
    */
    'email' => [
        // 提醒信冷卻時間（小時）
        'reminder_cooldown_hours' => env('EMAIL_REMINDER_COOLDOWN', 24),

        // 最大重試次數
        'max_retry_attempts' => env('EMAIL_MAX_RETRY', 3),

        // 郵件模板設定
        'templates' => [
            'invitation' => 'emails.recommendation-invitation',
            'reminder' => 'emails.recommendation-reminder',
            'submission_notification' => 'emails.recommendation-submitted',
        ],

        // 郵件主旨前綴
        'subject_prefix' => env('EMAIL_SUBJECT_PREFIX', '[推薦函系統]'),
    ],

    /*
    |--------------------------------------------------------------------------
    | PDF 生成設定
    |--------------------------------------------------------------------------
    */
    'pdf' => [
        // PDF 生成引擎 (dompdf, snappy)
        'engine' => env('PDF_ENGINE', 'dompdf'),

        // 預設紙張大小
        'paper_size' => env('PDF_PAPER_SIZE', 'A4'),

        // 預設方向 (portrait, landscape)
        'orientation' => env('PDF_ORIENTATION', 'portrait'),

        // PDF 品質設定
        'dpi' => env('PDF_DPI', 96),

        // 字型設定
        'font' => [
            'default' => env('PDF_DEFAULT_FONT', 'DejaVu Sans'),
            'chinese' => env('PDF_CHINESE_FONT', 'Microsoft JhengHei'),
        ],

        // 檔案儲存設定
        'storage' => [
            'disk' => env('PDF_STORAGE_DISK', 'local'),
            'path' => env('PDF_STORAGE_PATH', 'recommendations'),
            'max_size' => env('PDF_MAX_SIZE', 10240), // KB
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 檔案上傳設定
    |--------------------------------------------------------------------------
    */
    'upload' => [
        // 允許的檔案類型
        'allowed_types' => ['pdf'],

        // 最大檔案大小 (KB)
        'max_size' => env('UPLOAD_MAX_SIZE', 10240), // 10MB

        // 上傳路徑
        'path' => env('UPLOAD_PATH', 'uploads/recommendations'),

        // 檔案命名規則
        'naming' => [
            'pattern' => 'recommendation_{applicant_id}_{recommender_id}_{timestamp}',
            'include_hash' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 快取設定
    |--------------------------------------------------------------------------
    */
    'cache' => [
        // 快取驅動
        'driver' => env('RECOMMENDATION_CACHE_DRIVER', 'database'),

        // 快取時間（分鐘）
        'ttl' => [
            'templates' => env('CACHE_TEMPLATES_TTL', 60), // 1 小時
            'statistics' => env('CACHE_STATISTICS_TTL', 15), // 15 分鐘
            'user_data' => env('CACHE_USER_DATA_TTL', 30), // 30 分鐘
        ],

        // 快取鍵前綴
        'prefix' => env('RECOMMENDATION_CACHE_PREFIX', 'rec_'),
    ],

    /*
    |--------------------------------------------------------------------------
    | 安全設定
    |--------------------------------------------------------------------------
    */
    'security' => [
        // 是否啟用 CSRF 保護
        'csrf_protection' => env('RECOMMENDATION_CSRF_PROTECTION', true),

        // 是否記錄敏感操作
        'log_sensitive_operations' => env('LOG_SENSITIVE_OPERATIONS', true),

        // IP 白名單（管理員功能）
        'admin_ip_whitelist' => env('ADMIN_IP_WHITELIST', ''),

        // 登入嘗試限制
        'login_attempts' => [
            'max_attempts' => env('LOGIN_MAX_ATTEMPTS', 5),
            'lockout_duration' => env('LOGIN_LOCKOUT_DURATION', 15), // 分鐘
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 資料驗證規則
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'recommender' => [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'title' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
        ],

        'recommendation' => [
            'department_name' => 'required|string|max:255',
            'program_type' => 'required|string|max:255',
            'application_form_id' => 'required|string|max:255',
        ],

        'questionnaire' => [
            'max_questions' => env('QUESTIONNAIRE_MAX_QUESTIONS', 50),
            'max_answer_length' => env('QUESTIONNAIRE_MAX_ANSWER_LENGTH', 5000),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 統計和報告設定
    |--------------------------------------------------------------------------
    */
    'statistics' => [
        // 是否啟用統計功能
        'enabled' => env('STATISTICS_ENABLED', true),

        // 統計資料保留期限（天）
        'retention_days' => env('STATISTICS_RETENTION_DAYS', 365),

        // 報告生成設定
        'reports' => [
            'auto_generate' => env('REPORTS_AUTO_GENERATE', false),
            'schedule' => env('REPORTS_SCHEDULE', 'weekly'),
            'recipients' => env('REPORTS_RECIPIENTS', ''),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 除錯和開發設定
    |--------------------------------------------------------------------------
    */
    'debug' => [
        // 是否啟用除錯模式
        'enabled' => env('RECOMMENDATION_DEBUG', false),

        // 是否記錄詳細日誌
        'verbose_logging' => env('RECOMMENDATION_VERBOSE_LOGGING', false),

        // 測試模式設定
        'test_mode' => [
            'enabled' => env('RECOMMENDATION_TEST_MODE', false),
            'mock_email' => env('RECOMMENDATION_MOCK_EMAIL', true),
            'mock_pdf' => env('RECOMMENDATION_MOCK_PDF', false),
        ],
    ],
];
