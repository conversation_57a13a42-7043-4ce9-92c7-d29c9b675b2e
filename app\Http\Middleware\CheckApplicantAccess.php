<?php

namespace App\Http\Middleware;

use App\Models\Applicant;
use App\Models\Recommender;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class CheckApplicantAccess
{
  /**
   * Handle an incoming request.
   *
   * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
   */
  public function handle(Request $request, Closure $next): Response
  {
    $user = Auth::user();

    Log::info('【Middleware】考生存取檢查中間件觸發', ['user' => $user?->id]);
    // Check if user is authenticated and has applicant role
    if (!$user || $user->role !== 'applicant') {
      Log::warning('【Middleware】考生存取檢查失敗：使用者或角色不符', ['user' => $user]);
      return redirect()->route('home')->with('error', '您沒有權限訪問此頁面。');
    }

    // Check if applicant session exists
    $applicantId = Session::get('applicant_id');
    if (!$applicantId) {
      Log::warning('【Middleware】考生會話不存在或已過期', ['user' => $user->id]);
      return redirect()->route('home')->with('error', '考生會話已過期，請重新登入。');
    }

    // Verify applicant exists and matches user email
    $applicant = Applicant::find($applicantId);
    if (!$applicant || $applicant->email !== $user->email) {
      Log::warning('【Middleware】考生存取檢查失敗：考生不存在或電子郵件不匹配', [
        'session_id' => $applicantId,
        'user_email' => $user->email,
      ]);
      Session::forget(['applicant_id', 'login_token']);
      Auth::logout();
      return redirect()->route('home')->with('error', '考生驗證失敗，請重新登入。');
    }

    return $next($request);
  }
}
