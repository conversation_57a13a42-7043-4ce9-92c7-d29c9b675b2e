## 🧪 資料庫初始化與測試資料

### 執行 Migration 清除資料表 並填入 Seed 資料

```bash
php artisan migrate:fresh --seed
```

---

FOOTER (以到底部提示)

功能：

- 推薦函與書審資料的PDF合併

```

盡量與PB脫鉤
- 讓作業人員能夠在不依賴PB的狀態下完成作業
-
```

TODO

- 排程配置(自動清理、自動同步)

=================================================
書審資料轉檔SOP

ssh登入exam.nuu.edu.tw

一、轉檔
切換使用者帳號
sudo su - www-data -s /bin/bash

切換至專案目錄
cd /home/<USER>/exam

執行轉檔指令說明
php artisan -h app:tran-upload-pdf
php artisan app:tran-upload-pdf <s71 or s72> <招生類別> <招生年度> <匯出資料類型 Xlsx, Xls, Ods, Csv, Html>

執行轉檔指令
s71 1 博士班考試招生
s71 3 碩士班考試招生
s71 B 原住民專班招生
s71 P 碩士在職專班招生
s72 D1 暑假轉學考
s72 D2 寒假轉學考
s72 E1 進修學士班申請入學招生
s72 E2 進修學士班一般入學招生

php artisan app:tran-upload-pdf s72 D1 114 Csv

二、複製檔案出來
回到管理者帳號
exit

切換至專案目錄-轉檔位置
cd /home/<USER>/exam/storage/app

將資料夾權限變更(做過可以跳過)
ls -l
sudo chmod 775 exam_data_tran
sudo chown exam:www-data exam_data_tran

將要匯出的檔案、資料夾權限變更
cd exam_data_tran
ls -l
sudo chown exam s72_D2_113_20241225132209_csv.zip
sudo chown -R exam s72_D2_113_csv

將檔案複製出來
整理加密(49502521)後，交給綜合業務組
=================================================
