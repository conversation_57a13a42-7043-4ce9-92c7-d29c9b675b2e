<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\QuestionnaireTemplate;
use App\Models\SystemLog;
use App\Models\SystemSetting;
use App\Services\PdfService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

/**
 * PDF管理控制器
 * 
 * 管理PDF模板、上傳和相關設定
 */
class PdfManagementController extends Controller
{
    /**
     * 顯示PDF管理頁面
     */
    public function index()
    {
        // 獲取問卷模板（用於PDF管理）
        $templates = QuestionnaireTemplate::orderBy('department_name')
            ->orderBy('program_type')
            ->get()
            ->map(function ($template) {
                return [
                    'id' => $template->id,
                    'template_name' => $template->template_name,
                    'department_name' => $template->department_name,
                    'program_type' => $template->program_type,
                    'is_active' => $template->is_active,
                    'created_at' => $template->created_at->format('Y-m-d H:i:s'),
                    'creator_name' => '系統',
                    'questions_count' => count($template->questions ?? [])
                ];
            });

        $breadcrumbs = [
            ['title' => '儀表板', 'href' => '/dashboard'],
            ['title' => 'PDF管理', 'href' => '/admin/pdf-management'],
        ];

        return Inertia::render('admin/pdf-management', [
            'templates' => $templates,
            'breadcrumbs' => $breadcrumbs,
            'pdf_settings' => [
                'max_upload_size' => config('recommendation.upload.max_size'),
                'allowed_types' => config('recommendation.upload.allowed_types'),
                'storage_path' => config('recommendation.pdf.storage.path'),
            ],
            'submission_settings' => [
                'allow_pdf_upload' => SystemSetting::isAllowPdfUpload(),
                'allow_questionnaire_submission' => SystemSetting::isAllowQuestionnaireSubmission(),
            ]
        ]);
    }

    /**
     * 上傳PDF模板
     */
    public function uploadTemplate(Request $request)
    {
        $request->validate([
            'template_file' => 'required|file|mimes:pdf|max:' . config('recommendation.upload.max_size'),
            'template_name' => 'required|string|max:255',
            'department_name' => 'required|string|max:255',
            'program_type' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        try {
            $file = $request->file('template_file');
            $pdfService = new PdfService();

            // 驗證PDF檔案
            if (!$pdfService->validatePdf($file->getRealPath())) {
                return back()->withErrors([
                    'template_file' => '請上傳有效的PDF檔案'
                ]);
            }

            // 生成檔案路徑
            $fileName = $this->generateTemplateFileName($request->template_name, $file->getClientOriginalExtension());
            $filePath = "pdf-templates/{$fileName}";

            // 存儲檔案
            $storedPath = Storage::disk('local')->putFileAs('pdf-templates', $file, $fileName);

            if (!$storedPath) {
                throw new \Exception('檔案上傳失敗');
            }

            // 記錄操作日誌
            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_CREATE,
                "管理員 {$user->name} 上傳了PDF模板",
                [
                    'template_name' => $request->template_name,
                    'department_name' => $request->department_name,
                    'program_type' => $request->program_type,
                    'file_path' => $storedPath,
                    'file_size' => $file->getSize()
                ]
            );

            return back()->with('success', 'PDF模板上傳成功');
        } catch (\Exception $e) {
            Log::error('PDF模板上傳失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->except(['template_file'])
            ]);

            SystemLog::logError(
                SystemLog::ACTION_CREATE,
                'PDF模板上傳失敗',
                $e
            );

            return back()->withErrors([
                'template_file' => '上傳失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 刪除PDF模板
     */
    public function deleteTemplate($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);

            // 檢查是否有相關的推薦函使用此模板
            $usageCount = $template->recommendationLetters()->count();

            if ($usageCount > 0) {
                return back()->withErrors([
                    'template' => "無法刪除模板，目前有 {$usageCount} 封推薦函正在使用此模板"
                ]);
            }

            $templateName = $template->template_name;
            $template->delete();

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_DELETE,
                "管理員 {$user->name} 刪除了PDF模板：{$templateName}",
                [
                    'template_id' => $id,
                    'template_name' => $templateName
                ]
            );

            return back()->with('success', 'PDF模板已刪除');
        } catch (\Exception $e) {
            Log::error('PDF模板刪除失敗', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'template' => '刪除失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 切換模板啟用狀態
     */
    public function toggleTemplate($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);
            $template->is_active = !$template->is_active;
            $template->save();

            $user = Auth::user();
            $action = $template->is_active ? '啟用' : '停用';

            SystemLog::logOperation(
                SystemLog::ACTION_UPDATE,
                "管理員 {$user->name} {$action}了PDF模板：{$template->template_name}",
                [
                    'template_id' => $id,
                    'is_active' => $template->is_active
                ]
            );

            return back()->with('success', "模板已{$action}");
        } catch (\Exception $e) {
            Log::error('PDF模板狀態切換失敗', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'template' => '操作失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 下載PDF模板
     */
    public function downloadTemplate($id)
    {
        try {
            $template = QuestionnaireTemplate::findOrFail($id);

            // 這裡可以實作下載邏輯
            // 如果模板有對應的PDF檔案，則下載
            // 否則可以生成一個示例PDF

            return back()->with('info', '下載功能開發中');
        } catch (\Exception $e) {
            Log::error('PDF模板下載失敗', [
                'template_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return back()->withErrors([
                'template' => '下載失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 生成模板檔案名稱
     */
    private function generateTemplateFileName(string $templateName, string $extension): string
    {
        $timestamp = now()->format('YmdHis');
        $safeName = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $templateName);
        return "template_{$safeName}_{$timestamp}.{$extension}";
    }

    /**
     * 獲取PDF設定資訊
     */
    public function getSettings()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'upload' => config('recommendation.upload'),
                'pdf' => config('recommendation.pdf'),
                'storage_info' => [
                    'disk' => config('recommendation.pdf.storage.disk'),
                    'path' => config('recommendation.pdf.storage.path'),
                    'available_space' => $this->getAvailableStorageSpace()
                ]
            ]
        ]);
    }

    /**
     * 更新提交方式設定
     */
    public function updateSubmissionSettings(Request $request)
    {
        $request->validate([
            'allow_pdf_upload' => 'required|boolean',
            'allow_questionnaire_submission' => 'required|boolean',
        ]);

        try {
            // 至少要允許一種提交方式
            if (!$request->allow_pdf_upload && !$request->allow_questionnaire_submission) {
                return back()->withErrors([
                    'submission_settings' => '至少要允許一種提交方式'
                ]);
            }

            SystemSetting::setAllowPdfUpload($request->allow_pdf_upload);
            SystemSetting::setAllowQuestionnaireSubmission($request->allow_questionnaire_submission);

            $user = Auth::user();
            SystemLog::logOperation(
                SystemLog::ACTION_UPDATE,
                "管理員 {$user->name} 更新了提交方式設定",
                [
                    'allow_pdf_upload' => $request->allow_pdf_upload,
                    'allow_questionnaire_submission' => $request->allow_questionnaire_submission,
                ]
            );

            return back()->with('success', '提交方式設定已更新');
        } catch (\Exception $e) {
            Log::error('提交方式設定更新失敗', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            SystemLog::logError(
                SystemLog::ACTION_UPDATE,
                '提交方式設定更新失敗',
                $e
            );

            return back()->withErrors([
                'submission_settings' => '設定更新失敗：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 獲取可用存儲空間
     */
    private function getAvailableStorageSpace(): array
    {
        try {
            $path = storage_path('app');

            $totalSpace = disk_total_space($path);
            $freeSpace = disk_free_space($path);
            $usedSpace = $totalSpace - $freeSpace;

            return [
                'total' => $totalSpace,
                'free' => $freeSpace,
                'used' => $usedSpace,
                'usage_percentage' => $totalSpace > 0 ? round(($usedSpace / $totalSpace) * 100, 2) : 0
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'free' => 0,
                'used' => 0,
                'usage_percentage' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
