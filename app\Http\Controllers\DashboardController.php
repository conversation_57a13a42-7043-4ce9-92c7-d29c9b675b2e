<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use App\Models\Recommender;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;

/**
 * 儀表板控制器(考生與推薦人)
 */
class DashboardController extends Controller
{
  // 主要功能：撈資料(依登入人身份索取資料)
  public function index(Request $request)
  {
    $user = Auth::user();
    $role = $user->role;

    $data = [];

    /**
     * 取得考生的推薦函
     * 
     * hint:
     * 登入是依據"學年度 + 考試編號 + idno(加密)"，因此不同年度/不同類別的考生會以不同的帳號進入系統
     * 所以只要用對應的登入人 ID 就可以獲取相關推薦函資料
     */

    // 如果是考生
    if ($role === 'applicant') {
      $applicantId = Session::get('applicant_id');
      $applicant = null;
      $recommendations = [];
      $applications = [];

      // 如果沒有從 session 獲取到考生 ID，則嘗試從資料庫中查找
      if (!$applicantId) {
        $applicant = Applicant::where('user_id', $user->id)->first();
        $applicantId = $applicant?->id;
      } else {
        $applicant = Applicant::find($applicantId);
      }

      if ($applicantId) {
        $recommendations = RecommendationLetter::where('applicant_id', $applicantId)
          ->with(['applicant.user', 'recommender'])
          ->get([
            'id',
            'applicant_id',
            'application_form_id',
            'department_name',
            'program_type',
            'recommender_department',
            'status',
            'recommender_email',
            'recommender_name',
            'recommender_title',
            'recommender_phone',
            'pdf_path',
            'submitted_at',
            'created_at',
            'updated_at'
          ]);
      }

      if ($applicant && $applicant->external_uid) {
        $applications = Session::get('applications', []); // 從考生登入時的 session 中獲取報名資料
      }

      $data['recommendations'] = $recommendations;
      $data['applications'] = $applications;
      $data['applicant_id'] = $applicantId;
      $data['applicant_info'] = $applicant ? [
        'user' => [
          'id' => $user->id,
          'role' => $user->role,
          'name' => $user->name,
          'email' => $user->email,
          'phone' => $applicant->phone,
        ]
      ] : null;
    }

    // 如果是推薦人
    if ($role === 'recommender') {
      $recommenderId = Session::get('recommender_id');
      $recommender = null;

      if ($recommenderId) {
        $recommender = Recommender::find($recommenderId);
      } else {
        $recommender = Recommender::where('email', $user->email)->first();
      }

      $recommendations = [];
      $questionnaireTemplates = [];

      if ($recommender) {
        // 取得推薦人相關的推薦函
        $recommendations = RecommendationLetter::where('recommender_email', $recommender->email)
          ->with(['applicant.user'])
          ->get([
            'id',
            'applicant_id',
            'application_form_id',
            'department_name',
            'program_type',
            'status',
            'pdf_path',
            'submitted_at',
            'created_at'
          ]);

        // 預載入問卷模板，按推薦函的系所和學程分組
        $departmentPrograms = $recommendations->map(function ($rec) {
          return [
            'department_name' => $rec->department_name,
            'program_type' => $rec->program_type,
          ];
        })->unique()->values();

        foreach ($departmentPrograms as $dp) {
          $template = QuestionnaireTemplate::where('department_name', $dp['department_name'])
            ->where('program_type', $dp['program_type'])
            ->where('is_active', true)
            ->first();

          if (!$template) {
            // 如果沒有找到特定的模板，使用預設模板
            $template = QuestionnaireTemplate::where('department_name', '通用')
              ->where('program_type', '一般')
              ->where('is_active', true)
              ->first();
          }

          if ($template) {
            // 確保 questions 是陣列格式
            if (is_string($template->questions)) {
              $template->questions = json_decode($template->questions, true);
            }

            $questionnaireTemplates["{$dp['department_name']}_{$dp['program_type']}"] = $template;
          }
        }
      }

      $data['recommendations'] = $recommendations;
      $data['recommender'] = $recommender;
      $data['questionnaire_templates'] = $questionnaireTemplates;
    }

    return Inertia::render('dashboard', [
      'recommendations' => $data['recommendations'] ?? [],
      'applications' => $data['applications'] ?? [],
      'applicant_id' => $data['applicant_id'] ?? null,
      'recommender' => $data['recommender'] ?? null,
      'questionnaire_templates' => $data['questionnaire_templates'] ?? [], // 暫不使用
      'applicant_info' => $data['applicant_info'] ?? null,
      'user_role' => $role,
    ]);
  }
}
