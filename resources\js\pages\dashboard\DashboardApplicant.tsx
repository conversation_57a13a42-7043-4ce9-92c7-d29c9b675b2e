import ConfirmationDialog from '@/components/ConfirmationDialog';
import { NoRecommendationsEmptyState } from '@/components/EmptyState';
import { ToastContainer } from '@/components/Toast';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useLanguage } from '@/hooks/use-language';
import { useToast } from '@/hooks/useToast';
import { SharedData } from '@/types';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import { AlertCircle, Clock, Plus, ChevronDown, ChevronUp, Building, Calendar, User, Mail, Phone, BarChart3 } from 'lucide-react';
import React, { useEffect, useState } from 'react';

// 型別定義
interface Recommendation {
    id: number;
    department_name: string; // 推薦函大分組
    program_type: string; // 推薦函小分組
    status: string;
    recommender_email?: string;
    recommender_name?: string;
    recommender_title?: string;
    recommender_phone?: string;
    recommender_department: string; // 推薦人所屬公司或組織
    pdf_path?: string;
    submitted_at?: string;
    created_at?: string;
    updated_at?: string;
}

// 外部 API 回傳的報名資料結構
interface ExternalApplication {
    autono: number;
    dep_no: string;
    exam_name: string;
    dep_name: string;
}

interface Group {
    name: string;
    data: Recommendation[];
    needRecs?: number; // 需要幾份推薦信

    // 未來擴充欄位
    exam_name?: string;
    dep_no?: string;
    autono?: number;
}

interface Registration {
    department: string;
    groups: Group[];
}

/**
 * 將外部 API 的報名資料轉換為前端使用的格式
 * 根據 dep_name 進行分組
 */
function transformExternalApplications(applications: ExternalApplication[]): Registration[] {
    if (!applications || applications.length === 0) {
        return [];
    }

    // 根據 dep_name 分組
    const groupedByDepartment = applications.reduce(
        (acc, app) => {
            const deptName = app.dep_name;
            if (!acc[deptName]) {
                acc[deptName] = [];
            }
            acc[deptName].push(app);
            return acc;
        },
        {} as Record<string, ExternalApplication[]>,
    );

    // 轉換為前端格式
    return Object.entries(groupedByDepartment).map(([department, apps]) => ({
        department,
        groups: apps.map((app) => ({
            name: app.exam_name,
            needRecs: 0, // 預設需要 1 份推薦函，未來可從 API 取得
            data: [],
            // 保留未來擴充欄位
            exam_name: app.exam_name,
            dep_no: app.dep_no,
            autono: app.autono,
        })),
    }));
}

// 工具函式：將推薦函套入註冊表資料中
function transformRegistrationData(registrations: Registration[], recommendations: Recommendation[]): Registration[] {
    return registrations.map((reg) => {
        const updatedGroups = reg.groups.map((group) => {
            const matched = recommendations.filter((rec) => rec.department_name === reg.department && rec.program_type === group.name);

            return {
                ...group,
                data: matched, // 可為空，但明確表示這個群組目前沒有推薦函
            };
        });

        return {
            ...reg,
            groups: updatedGroups,
        };
    });
}

// Component
export default function DashboardApplicant() {
    const { recommendations, applications, applicant_info } = usePage<SharedData>().props;
    const { t } = useLanguage();
    const toast = useToast();

    // 簡單的錯誤處理函數
    const getErrorMessage = (errors: any): string => {
        if (typeof errors === 'string') return errors;
        if (errors && typeof errors === 'object') {
            // 取得第一個錯誤訊息
            const firstError = Object.values(errors)[0];
            if (Array.isArray(firstError)) {
                return firstError[0] as string;
            }
            return firstError as string;
        }
        return '發生未知錯誤，請稍後再試';
    };

    const [showFormFor, setShowFormFor] = useState<{ department: string; group: string } | null>(null);
    const [registrations, setRegistrations] = useState<Registration[]>([]);
    const [expandedCards, setExpandedCards] = useState<Set<number>>(new Set()); // 追蹤展開的卡片

    // 檢查是否應該顯示狀態概覽（只有當有推薦函需求時才顯示）
    const shouldShowOverview = () => {
        return registrations.some((registration) => registration.groups.some((group) => group.needRecs > 0));
    };

    // Confirmation dialog states
    const [confirmDialog, setConfirmDialog] = useState<{
        isOpen: boolean;
        type: 'remind' | 'withdraw' | null;
        recommendation: Recommendation | null;
        isLoading: boolean;
    }>({
        isOpen: false,
        type: null,
        recommendation: null,
        isLoading: false,
    });

    // Force light theme for applicant view
    useEffect(() => {
        document.documentElement.classList.add('light');
        return () => {
            document.documentElement.classList.remove('light');
        };
    }, []);

    // 狀態顯示函數 - 包含超時檢查
    const getStatusBadge = (rec: Recommendation) => {
        const status = rec.status;
        const overdue = isOverdue(rec);

        // 如果是pending狀態且超過7天，顯示為未回應樣式
        if (status === 'pending' && overdue) {
            return (
                <Badge variant="secondary" className="border-orange-300 bg-orange-100 text-orange-700">
                    ⏰ 未回應 ({getDaysAgo(rec)}天)
                </Badge>
            );
        }

        switch (status) {
            case 'pending': // 待處理
                return (
                    <Badge variant="secondary" className="border-slate-300 bg-slate-100 text-slate-700">
                        {t('recommendations.status.pending')}
                    </Badge>
                );
            case 'submitted': // 已提交
                return (
                    <Badge variant="secondary" className="border-emerald-300 bg-emerald-100 text-emerald-700">
                        ✓ {t('recommendations.status.submitted')}
                    </Badge>
                );
            case 'withdrawn': // 已撤回
                return (
                    <Badge variant="secondary" className="border-gray-300 bg-gray-100 text-gray-700">
                        {t('recommendations.status.withdrawn')}
                    </Badge>
                );
            case 'no_response': // 未回應
                return (
                    <Badge variant="secondary" className="border-orange-300 bg-orange-100 text-orange-700">
                        ⏰ {t('recommendations.status.no_response')}
                    </Badge>
                );
            case 'declined': // 已婉拒
                return (
                    <Badge variant="secondary" className="border-red-400 bg-red-200 text-red-700">
                        ✗ {t('recommendations.status.declined')}
                    </Badge>
                );

            default:
                return (
                    <Badge variant="secondary" className="border-gray-300 bg-gray-100 text-gray-700">
                        {status}
                    </Badge>
                );
        }
    };

    // 進度顯示函數
    const getProgressInfo = (group: Group) => {
        const completed = group.data.filter((rec) => rec.status === 'submitted').length;
        const total = group.needRecs;
        const isComplete = completed >= total;
        const remainingNeeded = Math.max(0, total - completed); // 基於已完成數量計算還需要的

        return {
            completed,
            total,
            remaining: remainingNeeded,
            isComplete,
            progressText: `${completed}/${total}`,
            progressColor: isComplete ? 'text-emerald-600' : completed > 0 ? 'text-slate-600' : 'text-gray-500',
        };
    };

    // 卡片樣式函數 - 包含超時檢查
    const getCardStyle = (rec: Recommendation) => {
        const status = rec.status;
        const overdue = isOverdue(rec);

        // 如果是pending狀態且超過7天，使用未回應樣式
        if (status === 'pending' && overdue) {
            return 'border-orange-200 bg-orange-50/30 shadow-sm';
        }

        switch (status) {
            case 'submitted':
                return 'border-emerald-200 bg-emerald-50/30 shadow-sm';
            case 'pending':
                return 'border-slate-200 bg-slate-50/30 shadow-sm';
            case 'withdrawn':
            case 'no_response':
                return 'border-gray-200 bg-gray-50/30 shadow-sm';
            case 'declined':
                return 'border-red-200 bg-red-50/30 shadow-sm';
            default:
                return 'border-gray-200 bg-white shadow-sm';
        }
    };

    const form = useForm({
        recommenderEmail: '',
        recommenderName: '',
        recommenderTitle: '',
        recommenderPhone: '',
        recommenderAffiliation: '',
        department: '',
        group: '',
    });

    // 初始化報名資料並套入推薦函資料
    useEffect(() => {
        if (!applications || applications.length === 0) {
            setRegistrations([]);
            return;
        }

        // 將外部 API 資料轉換為前端格式
        const initialRegistrations = transformExternalApplications(applications as ExternalApplication[]);

        // 如果有推薦函資料，立即套入
        if (recommendations && recommendations.length > 0) {
            const updated = transformRegistrationData(initialRegistrations, recommendations);
            setRegistrations(updated);
        } else {
            setRegistrations(initialRegistrations);
        }
    }, [applications, recommendations]);

    // 切換卡片展開狀態
    const toggleCardExpansion = (recommendationId: number) => {
        setExpandedCards((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(recommendationId)) {
                newSet.delete(recommendationId);
            } else {
                newSet.add(recommendationId);
            }
            return newSet;
        });
    };

    // 檢查推薦函是否超過7天未回應
    const isOverdue = (rec: Recommendation): boolean => {
        if (rec.status !== 'pending') return false;

        const createdDate = new Date(rec.created_at || '');
        const now = new Date();
        const daysDiff = Math.floor((now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));

        return daysDiff >= 7;
    };

    // 計算推薦函建立天數
    const getDaysAgo = (rec: Recommendation): number => {
        const createdDate = new Date(rec.created_at || '');
        const now = new Date();
        return Math.floor((now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
    };

    const openForm = (department: string, group: string) => {
        setShowFormFor({ department, group });
        form.setData({
            recommenderEmail: '',
            recommenderName: '',
            recommenderTitle: '',
            recommenderPhone: '',
            recommenderAffiliation: '',
            department,
            group,
        });
    };

    const closeForm = () => {
        setShowFormFor(null);
    };

    const submitForm = (e: React.FormEvent) => {
        e.preventDefault();

        // 確保使用正確的 department 和 group 值
        const department = showFormFor?.department || form.data.department;
        const group = showFormFor?.group || form.data.group;

        console.log('Submitting form with:', {
            department,
            group,
            department_name: department,
            application_form_id: `${department}_${group}_${Date.now()}`,
        });

        router.post(
            '/recommendations',
            {
                recommender_email: form.data.recommenderEmail,
                recommender_name: form.data.recommenderName,
                recommender_title: form.data.recommenderTitle,
                recommender_phone: form.data.recommenderPhone,
                recommender_department: form.data.recommenderAffiliation,
                department_name: department,
                program_type: group,
                application_form_id: `${department}_${group}_${Date.now()}`,
            },
            {
                onSuccess: (response: unknown) => {
                    // Handle enhanced response with recommender info
                    const data = (response as any)?.props?.flash || (response as any) || {};

                    if (data.message) {
                        toast.success(data.message);
                    } else {
                        toast.success('推薦函邀請已成功創建並發送');
                    }

                    // Optionally show recommender login link if created
                    if (data.recommender?.created && data.recommender?.login_url) {
                        toast.info(`推薦人帳號已建立，登入連結：${data.recommender.login_url}`, {
                            duration: 10000,
                            action: {
                                label: '複製連結',
                                onClick: () => {
                                    navigator.clipboard.writeText(data.recommender.login_url);
                                    toast.success('登入連結已複製到剪貼簿');
                                },
                            },
                        });
                    }

                    closeForm();
                    // Refresh the page to show updated data
                    router.reload();
                },
                onError: (errors) => {
                    console.error('Error creating recommendation:', errors);
                    const errorMessage = getErrorMessage(errors);
                    toast.error(errorMessage);
                },
            },
        );
    };

    // Open confirmation dialog
    const openConfirmDialog = (type: 'remind' | 'withdraw', recommendation: Recommendation) => {
        setConfirmDialog({
            isOpen: true,
            type,
            recommendation,
            isLoading: false,
        });
    };

    // Close confirmation dialog
    const closeConfirmDialog = () => {
        setConfirmDialog({
            isOpen: false,
            type: null,
            recommendation: null,
            isLoading: false,
        });
    };

    // Handle confirmed action
    const handleConfirmedAction = () => {
        if (!confirmDialog.recommendation || !confirmDialog.type) return;

        setConfirmDialog((prev) => ({ ...prev, isLoading: true }));

        const rec = confirmDialog.recommendation;

        if (confirmDialog.type === 'remind') {
            router.post(
                `/recommendations/${rec.id}/remind`,
                {},
                {
                    onSuccess: () => {
                        toast.success(t('recommendations.messages.reminderSent'));
                        closeConfirmDialog();
                        router.reload();
                    },
                    onError: (errors) => {
                        console.error('Error sending reminder:', errors);
                        const errorMessage = getErrorMessage(errors);
                        toast.error(errorMessage || t('recommendations.messages.reminderFailed'));
                        setConfirmDialog((prev) => ({ ...prev, isLoading: false, isOpen: false }));
                    },
                },
            );
        } else if (confirmDialog.type === 'withdraw') {
            router.post(
                `/recommendations/${rec.id}/withdraw`,
                {},
                {
                    onSuccess: () => {
                        toast.success(t('recommendations.messages.withdrawn'));
                        closeConfirmDialog();
                        router.reload();
                    },
                    onError: (errors) => {
                        console.error('Error withdrawing recommendation:', errors);
                        const errorMessage = getErrorMessage(errors);
                        toast.error(errorMessage || t('recommendations.messages.withdrawFailed'));
                        setConfirmDialog((prev) => ({ ...prev, isLoading: false, isOpen: false }));
                    },
                },
            );
        }
    };

    return (
        <div className="p-4">
            <Head title="推薦函列表"></Head>
            {/* Toast Container */}
            <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} position="top-right" />
            {/* 提醒訊息 */}
            <div className="mb-6 rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4 shadow-sm">
                <div className="flex items-start">
                    <div className="flex-shrink-0">
                        <AlertCircle className="h-5 w-5 text-blue-400" />
                    </div>
                    <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-blue-800">{t('dashboard.applicant.importantNotice')}</h3>
                        <div className="mt-2 text-sm text-blue-700">
                            <ul className="list-disc space-y-1 pl-5">
                                <li>
                                    <strong>{t('dashboard.applicant.deadline')}：</strong>
                                    <span className="font-semibold text-red-600">{t('dashboard.applicant.deadlineDate')}</span>（
                                    {t('dashboard.applicant.deadlineNote')}）
                                </li>
                                <li>
                                    <strong>{t('dashboard.applicant.advanceTime')}：</strong>
                                    {t('dashboard.applicant.advanceTimeNote')}
                                </li>
                                <li>
                                    <strong>{t('dashboard.applicant.requiredCount')}：</strong>
                                    {t('dashboard.applicant.requiredCountNote')}
                                </li>
                                <li>
                                    <strong>{t('dashboard.applicant.reminderFunction')}：</strong>
                                    {t('dashboard.applicant.reminderNote')}
                                </li>
                                <li>
                                    <strong>{t('dashboard.applicant.contact')}：</strong>
                                    {t('dashboard.applicant.contactNote')}
                                    <a href="tel:02-1234-5678" className="ml-1 font-medium text-blue-600 hover:text-blue-500">
                                        02-1234-5678
                                    </a>{' '}
                                    或
                                    <a href="mailto:<EMAIL>" className="ml-1 font-medium text-blue-600 hover:text-blue-500">
                                        <EMAIL>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div className="mt-3 flex items-center text-xs text-blue-600">
                            <Clock className="mr-1 h-3 w-3" />
                            {t('dashboard.applicant.systemTime')}：
                            {new Date().toLocaleString('zh-TW', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: false,
                            })}
                        </div>
                    </div>
                </div>
            </div>

            {/* 使用者基本資訊 */}
            <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <div className="mb-4 flex items-center gap-2">
                    <User className="h-5 w-5 text-gray-600" />
                    <h3 className="text-lg font-semibold text-gray-900">考生基本資訊</h3>
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {/* 姓名 */}
                    <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                            <User className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <p className="text-xs font-medium text-gray-500">姓名</p>
                            <p className="truncate text-sm font-medium text-gray-900">{applicant_info.user.name}</p>
                        </div>
                    </div>

                    {/* 電子郵件 */}
                    <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                            <Mail className="h-4 w-4 text-green-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <p className="text-xs font-medium text-gray-500">電子郵件</p>
                            <p className="truncate text-sm font-medium text-gray-900">{applicant_info.user.email}</p>
                        </div>
                    </div>

                    {/* 電話 */}
                    {applicant_info.user.phone && (
                        <div className="flex items-center gap-3 rounded-lg bg-gray-50 p-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100">
                                <Phone className="h-4 w-4 text-orange-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                                <p className="text-xs font-medium text-gray-500">電話</p>
                                <p className="truncate text-sm font-medium text-gray-900">{applicant_info.user.phone}</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 狀態概覽 - 只有當有推薦函需求時才顯示 */}
            {shouldShowOverview() && (
                <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <div className="mb-4 flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-gray-600" />
                        <h3 className="text-lg font-semibold text-gray-900">{t('dashboard.applicant.overallProgress')}</h3>
                    </div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {registrations.map((registration) =>
                            registration.groups
                                .filter((group) => group.needRecs > 0) // 只顯示有推薦函需求的群組
                                .map((group) => {
                                    const progress = getProgressInfo(group);
                                    const isComplete = progress.isComplete;
                                    const hasIncomplete = progress.remaining > 0;

                                    return (
                                        <div
                                            key={`${registration.department}-${group.name}`}
                                            className={`rounded-lg border p-4 transition-all duration-200 ${
                                                isComplete
                                                    ? 'border-emerald-200 bg-emerald-50 shadow-sm'
                                                    : hasIncomplete
                                                      ? 'border-amber-200 bg-amber-50 shadow-sm'
                                                      : 'border-gray-200 bg-gray-50'
                                            }`}
                                        >
                                            <div className="flex items-start justify-between">
                                                <div className="min-w-0 flex-1">
                                                    <h4 className="truncate text-sm font-medium text-gray-900">{registration.department}</h4>
                                                </div>
                                                <Badge variant="outline" className={`${progress.progressColor} border-current text-xs`}>
                                                    {progress.progressText}
                                                </Badge>
                                            </div>
                                        </div>
                                    );
                                }),
                        )}
                    </div>
                </div>
            )}

            {/* 推薦函列表 */}
            <div className="space-y-8">
                {registrations.map((registration, regIndex) => (
                    <section
                        key={`${registration.department}-${registration.groups[0].name}`}
                        className={`${regIndex > 0 ? 'border-t border-gray-200 pt-8' : ''}`}
                    >
                        <div className="space-y-8">
                            {registration.groups.map((group, groupIndex) => {
                                return (
                                    <div key={group.name} className={`${groupIndex > 0 ? 'border-t border-gray-100 pt-6' : ''}`}>
                                        {/* 學程標題卡片 */}
                                        <div className="mb-4">
                                            <div className="flex justify-between">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex-1 border-l-4 border-gray-500 pl-3">
                                                        <h3 className="text-lg font-semibold text-gray-900">{registration.department}</h3>
                                                        <p className="text-sm text-gray-600">{group.name}</p>
                                                    </div>
                                                </div>
                                                {/* 添加推薦人按鈕 */}
                                                <div className="flex-shrink-0">
                                                    {group.data.length != 0 && (
                                                        <Button
                                                            variant={'outline'}
                                                            onClick={() => openForm(registration.department, group.name)}
                                                            size="sm"
                                                            title={t('dashboard.applicant.addRecommender')}
                                                        >
                                                            <Plus className="h-4 w-4 sm:mr-2" />
                                                            <span className="hidden sm:inline">{t('dashboard.applicant.addRecommender')}</span>
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="space-y-4">
                                            {group.data.length === 0 ? (
                                                <NoRecommendationsEmptyState
                                                    userRole="applicant"
                                                    onCreateNew={() => openForm(registration.department, group.name)}
                                                />
                                            ) : (
                                                group.data.map((rec) => {
                                                    const isExpanded = expandedCards.has(rec.id);
                                                    return (
                                                        <Card key={rec.id} className={getCardStyle(rec)}>
                                                            <CardContent className="py-0">
                                                                {/* 卡片標題列 - 始終顯示 */}
                                                                <div
                                                                    className="-m-2 flex cursor-pointer items-center justify-between rounded-lg p-3 transition-all duration-200 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50"
                                                                    onClick={() => toggleCardExpansion(rec.id)}
                                                                >
                                                                    <div className="flex min-w-0 flex-1 items-center gap-4">
                                                                        <div className="min-w-0 flex-1">
                                                                            <h4 className="truncate text-lg font-semibold text-gray-900">
                                                                                {rec.recommender_name || '推薦人姓名未提供'}
                                                                            </h4>
                                                                            {rec.recommender_title && (
                                                                                <p className="truncate text-sm text-gray-600">
                                                                                    {rec.recommender_title} · {rec.recommender_department}
                                                                                </p>
                                                                            )}
                                                                        </div>
                                                                        {getStatusBadge(rec)}
                                                                    </div>
                                                                    <div className="flex flex-shrink-0 items-center gap-2">
                                                                        {isExpanded ? (
                                                                            <ChevronUp className="h-5 w-5 text-gray-400 transition-transform duration-200" />
                                                                        ) : (
                                                                            <ChevronDown className="h-5 w-5 text-gray-400 transition-transform duration-200" />
                                                                        )}
                                                                    </div>
                                                                </div>

                                                                {/* 詳細資訊 - 可收合 */}
                                                                {isExpanded && (
                                                                    <div className="mt-4 space-y-4 border-t border-gray-200 pt-4">
                                                                        {/* 推薦人詳細資訊 */}
                                                                        <div className="rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
                                                                            <h5 className="mb-4 flex items-center gap-2 text-sm font-semibold text-blue-900">
                                                                                <Building className="h-4 w-4" />
                                                                                推薦人資訊
                                                                            </h5>
                                                                            <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                                                                                <div className="flex items-center gap-3 rounded-md bg-white/60 p-3">
                                                                                    <Mail className="h-4 w-4 text-purple-600" />
                                                                                    <div>
                                                                                        <p className="text-xs font-medium text-gray-500">電子郵件</p>
                                                                                        <a
                                                                                            href={`mailto:${rec.recommender_email}`}
                                                                                            className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline"
                                                                                        >
                                                                                            {rec.recommender_email}
                                                                                        </a>
                                                                                    </div>
                                                                                </div>
                                                                                {rec.recommender_phone && (
                                                                                    <div className="flex items-center gap-3 rounded-md bg-white/60 p-3">
                                                                                        <Phone className="h-4 w-4 text-orange-600" />
                                                                                        <div>
                                                                                            <p className="text-xs font-medium text-gray-500">
                                                                                                聯絡電話
                                                                                            </p>
                                                                                            <p className="text-sm font-medium text-gray-900">
                                                                                                {rec.recommender_phone}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </div>

                                                                        {/* 時程資訊 */}
                                                                        <div
                                                                            className={`rounded-lg p-4 ${isOverdue(rec) ? 'border border-orange-200 bg-orange-50' : 'bg-gray-100'}`}
                                                                        >
                                                                            <h5 className="mb-3 flex items-center gap-2 text-sm font-medium text-gray-900">
                                                                                <Calendar className="h-4 w-4" />
                                                                                處理時程
                                                                                {isOverdue(rec) && (
                                                                                    <span className="text-xs font-medium text-orange-600">
                                                                                        (已超過 {getDaysAgo(rec)} 天)
                                                                                    </span>
                                                                                )}
                                                                            </h5>
                                                                            <div className="space-y-2">
                                                                                {/* 邀請建立時間 */}
                                                                                <div className="flex items-center justify-between text-sm">
                                                                                    <span className="text-gray-600">邀請建立：</span>
                                                                                    <span className="font-medium text-gray-900">
                                                                                        {new Date(rec.created_at || '').toLocaleString('zh-TW')}
                                                                                    </span>
                                                                                </div>

                                                                                {/* Email發送狀態 */}
                                                                                <div className="flex items-center justify-between text-sm">
                                                                                    <span className="text-gray-600">Email狀態：</span>
                                                                                    <span className="font-medium text-green-600">✓ 已發送邀請</span>
                                                                                </div>

                                                                                {/* 最後更新時間 */}
                                                                                {rec.updated_at && rec.updated_at !== rec.created_at && (
                                                                                    <div className="flex items-center justify-between text-sm">
                                                                                        <span className="text-gray-600">最後更新：</span>
                                                                                        <span className="font-medium text-gray-900">
                                                                                            {new Date(rec.updated_at).toLocaleString('zh-TW')}
                                                                                        </span>
                                                                                    </div>
                                                                                )}

                                                                                {/* 完成時間 */}
                                                                                {rec.submitted_at && (
                                                                                    <div className="flex items-center justify-between text-sm">
                                                                                        <span className="text-gray-600">完成時間：</span>
                                                                                        <span className="font-medium text-green-600">
                                                                                            {new Date(rec.submitted_at).toLocaleString('zh-TW')}
                                                                                        </span>
                                                                                    </div>
                                                                                )}
                                                                                {/* 超時警告 */}
                                                                                {isOverdue(rec) && (
                                                                                    <div className="mt-3 rounded-md border border-orange-200 bg-orange-100 p-3">
                                                                                        <div className="flex items-start gap-2">
                                                                                            <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-orange-600" />
                                                                                            <div className="text-sm">
                                                                                                <p className="mb-1 font-medium text-orange-800">
                                                                                                    推薦人可能未收到邀請或需要提醒
                                                                                                </p>
                                                                                                <p className="text-xs leading-relaxed text-orange-700">
                                                                                                    建議您：
                                                                                                    <br />
                                                                                                    • 檢查推薦人信箱是否正確
                                                                                                    <br />
                                                                                                    • 主動聯繫推薦人確認收信狀況
                                                                                                    <br />• 使用下方「發送提醒」功能
                                                                                                </p>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </div>

                                                                        {/* 操作按鈕區域 */}
                                                                        {rec.status === 'pending' && (
                                                                            <div className="flex flex-col gap-2 sm:flex-row sm:justify-end sm:gap-3">
                                                                                <Button
                                                                                    size="sm"
                                                                                    variant="outline"
                                                                                    className="border-slate-400 bg-slate-50 text-slate-700 hover:border-slate-500 hover:bg-slate-100"
                                                                                    onClick={() => openConfirmDialog('remind', rec)}
                                                                                >
                                                                                    {t('dashboard.applicant.sendReminder')}
                                                                                </Button>
                                                                                <Button
                                                                                    size="sm"
                                                                                    variant="outline"
                                                                                    className="border-red-400 bg-red-50 text-red-700 hover:border-red-500 hover:bg-red-100"
                                                                                    onClick={() => openConfirmDialog('withdraw', rec)}
                                                                                >
                                                                                    {t('dashboard.applicant.withdrawRecommendation')}
                                                                                </Button>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                )}
                                                            </CardContent>
                                                        </Card>
                                                    );
                                                })
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </section>
                ))}
            </div>
            {/* Recommendation Form Modal */}
            {showFormFor !== null && (
                <div
                    className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4"
                    role="dialog"
                    aria-modal="true"
                    aria-labelledby="modal-title"
                >
                    <div className="w-full max-w-lg scale-100 transform rounded-lg bg-white p-6 shadow-xl transition-transform">
                        <h2 id="modal-title" className="mb-6 text-xl font-bold text-gray-900">
                            {t('dashboard.applicant.addRecommender')}
                        </h2>
                        <form onSubmit={submitForm} className="space-y-4">
                            {/* 顯示當前選擇的群組信息 */}
                            <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                                <p className="text-sm text-blue-800">
                                    {form.data.group} – {form.data.department}
                                </p>
                            </div>

                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderName">
                                    {t('recommendations.form.recommenderName')} <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="recommenderName"
                                    type="text"
                                    value={form.data.recommenderName}
                                    onChange={(e) => form.setData('recommenderName', e.target.value)}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    required
                                />
                            </div>
                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderEmail">
                                    {t('recommendations.form.recommenderEmail')} <span className="text-red-500">*</span>
                                </label>
                                <input
                                    id="recommenderEmail"
                                    type="email"
                                    value={form.data.recommenderEmail}
                                    onChange={(e) => form.setData('recommenderEmail', e.target.value)}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                    required
                                />
                            </div>
                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderTitle">
                                    {t('recommendations.form.recommenderTitle')}
                                </label>
                                <input
                                    id="recommenderTitle"
                                    type="text"
                                    value={form.data.recommenderTitle}
                                    onChange={(e) => form.setData('recommenderTitle', e.target.value)}
                                    placeholder="例如：教授、經理等"
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                />
                            </div>

                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderAffiliation">
                                    {t('recommendations.form.recommenderAffiliation')}
                                </label>
                                <input
                                    id="recommenderAffiliation"
                                    type="text"
                                    value={form.data.recommenderAffiliation || ''}
                                    onChange={(e) => form.setData('recommenderAffiliation', e.target.value)}
                                    placeholder="例如：國立台灣大學資訊工程學系、某某公司技術部等"
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                />
                            </div>
                            <div>
                                <label className="mb-2 block font-semibold text-gray-700" htmlFor="recommenderPhone">
                                    {t('recommendations.form.recommenderPhone')}
                                </label>
                                <input
                                    id="recommenderPhone"
                                    type="text"
                                    value={form.data.recommenderPhone}
                                    onChange={(e) => form.setData('recommenderPhone', e.target.value)}
                                    className="w-full rounded border border-gray-300 p-2 text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-400 focus:outline-none"
                                />
                            </div>

                            <div className="flex justify-end space-x-3">
                                <button
                                    type="submit"
                                    className="rounded bg-green-600 px-5 py-2 text-white shadow-sm transition-colors hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-1 focus:outline-none"
                                >
                                    {t('recommendations.form.submit')}
                                </button>
                                <button
                                    type="button"
                                    className="rounded bg-gray-300 px-5 py-2 font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-400 focus:ring-2 focus:ring-gray-400 focus:ring-offset-1 focus:outline-none"
                                    onClick={closeForm}
                                >
                                    {t('recommendations.form.cancel')}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
            {/* Confirmation Dialog */}
            <ConfirmationDialog
                isOpen={confirmDialog.isOpen}
                onClose={closeConfirmDialog}
                onConfirm={handleConfirmedAction}
                title={
                    confirmDialog.type === 'remind'
                        ? t('recommendations.messages.confirm_send_reminder')
                        : t('recommendations.messages.confirm_withdraw')
                }
                message={
                    confirmDialog.type === 'remind'
                        ? `${t('recommendations.messages.remind_confirmation_message')} ${confirmDialog.recommendation?.recommender_name || ''}？`
                        : `${t('recommendations.messages.withdraw_confirmation_message')} ${confirmDialog.recommendation?.recommender_name || ''}？`
                }
                confirmText={
                    confirmDialog.type === 'remind' ? t('dashboard.applicant.sendReminder') : t('dashboard.applicant.withdrawRecommendation')
                }
                type={confirmDialog.type === 'withdraw' ? 'danger' : 'warning'}
                isLoading={confirmDialog.isLoading}
            />
        </div>
    );
}
